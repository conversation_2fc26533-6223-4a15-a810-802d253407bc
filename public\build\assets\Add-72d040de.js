import{r as u,o as l,c as i,a as o,u as s,w as d,F as k,Z as $,b as e,g as S,h as q,e as E,f as p,A as M,d as B,t as C}from"./app-78b9617f.js";import{_ as A,b as F}from"./AdminLayout-52ecaa4e.js";import{_ as y}from"./InputError-9ea1a4ce.js";import{_ as m}from"./InputLabel-e6e35589.js";import{P as T}from"./PrimaryButton-5ca54851.js";import{_ as b}from"./TextInput-f897977c.js";import{M as D}from"./Modal-e411db4b.js";import{u as L}from"./index-322a58be.js";import{Q as U}from"./vue-quill.snow-15e53a1b.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const z={class:"animate-top max-w-3xl mx-auto"},P={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},I={class:"border-b border-gray-200 pb-4 mb-6"},Q={class:"flex items-center gap-3"},W=e("h2",{class:"text-xl font-bold text-gray-900"},"Add New Sequence Step",-1),Y={class:"ml-auto flex items-center justify-end gap-x-6"},Z=["onSubmit"],G={class:"space-y-8"},H={class:"grid grid-cols-1 gap-6 sm:grid-cols-2"},J={class:"sm:col-span-1"},K={class:"sm:col-span-1"},O={class:"sm:grid-cols-1"},R={class:"sm:col-span-6"},X={class:"flex mt-6 items-center justify-between"},ee={class:"ml-auto flex items-center justify-end gap-x-6"},te=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),se={key:0,class:"text-sm text-gray-600"},oe={class:"p-6 relative"},ae=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),ne=[ae],le=e("h2",{class:"text-lg font-medium text-gray-900"},"Email Tags",-1),ie={class:"mt-4 overflow-x-auto sm:rounded-lg"},re={class:"w-full text-sm text-left text-gray-500"},de=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50"},[e("tr",null,[e("th",{class:"px-4 py-2 text-gray-900"},"Tag Name"),e("th",{class:"px-4 py-2 text-gray-900"},"Description")])],-1),ce={key:0},ue=e("td",{colspan:"2",class:"px-4 py-4 text-center text-gray-500"}," No Email Tags Found. ",-1),pe=[ue],me={class:"divide-y divide-gray-300 bg-white"},_e={class:"px-4 py-2"},ve={class:"flex items-center space-x-2"},ge=["onClick"],fe={key:0,class:"text-green-600 text-xs"},ye={class:"px-4 py-2"},be={class:"flex items-center space-x-2"},Ee={__name:"Add",props:["sequence_id","nextStepNumber","tags"],setup(c){const x=c,t=L("post","/sequence-step",{sequence_id:x.sequence_id,step_number:x.nextStepNumber,days_after_previous_step:"",subject:"",content:""});t.content=`
<p>{email}</p>

<p>Dear Team,</p>

<p>[Your email content here]</p>

<p>{first_name} {last_name}</p>
<p>{designation}</p>
<p>{organization_name}</p>
<p>{city}, {country}</p>

<p>Website: {organization_website_url}</p>
<p>Website Linkedin: {organization_linkedin_url}</p>
<p>LinkedIn: {linkedin_url}</p>`;const _=u(!1),h=u(null),V=r=>{h.value=r,_.value=!0},w=()=>{_.value=!1,h.value=null},v=u(null),g=u(""),j=(r,a,n)=>{navigator.clipboard.writeText(r).then(()=>{v.value=a,g.value=n,setTimeout(()=>{v.value=null,g.value=""},2e3)})},N=()=>{t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})};return(r,a)=>(l(),i(k,null,[o(s($),{title:"Add Sequence Step"}),o(A,null,{default:d(()=>[e("div",z,[e("div",P,[e("div",I,[e("div",Q,[W,e("div",Y,[o(T,{onClick:a[0]||(a[0]=n=>V(r.tag))},{default:d(()=>[S(" Email Tags ")]),_:1})])])]),e("form",{onSubmit:q(N,["prevent"])},[e("div",G,[e("div",H,[e("div",J,[o(m,{value:"Step Number"}),o(b,{id:"step_number",type:"number",modelValue:s(t).step_number,"onUpdate:modelValue":a[1]||(a[1]=n=>s(t).step_number=n),class:"w-full bg-gray-50",disabled:""},null,8,["modelValue"]),o(y,{message:s(t).errors.step_number,class:"mt-2"},null,8,["message"])]),e("div",K,[o(m,{for:"days_after_previous_step",value:"Day After Previous Step"}),o(b,{id:"days_after_previous_step",type:"text",modelValue:s(t).days_after_previous_step,"onUpdate:modelValue":a[2]||(a[2]=n=>s(t).days_after_previous_step=n),onChange:a[3]||(a[3]=n=>s(t).validate("days_after_previous_step"))},null,8,["modelValue"]),s(t).invalid("days_after_previous_step")?(l(),E(y,{key:0,message:s(t).errors.days_after_previous_step},null,8,["message"])):p("",!0)])]),e("div",O,[o(m,{value:"Email Subject"}),o(b,{id:"subject",type:"text",modelValue:s(t).subject,"onUpdate:modelValue":a[4]||(a[4]=n=>s(t).subject=n),class:"w-full",placeholder:"Enter email subject line",onChange:a[5]||(a[5]=n=>s(t).validate("subject"))},null,8,["modelValue"]),o(y,{message:s(t).errors.subject,class:"mt-2"},null,8,["message"])]),e("div",R,[o(m,{value:"Email Content"}),o(s(U),{content:s(t).content,"onUpdate:content":a[6]||(a[6]=n=>s(t).content=n),contentType:"html",theme:"snow",toolbar:"essential"},null,8,["content"])]),e("div",X,[e("div",ee,[o(F,{href:r.route("sequence-step.show",{id:c.sequence_id})},{svg:d(()=>[te]),_:1},8,["href"]),o(T,{disabled:s(t).processing},{default:d(()=>[S("Save")]),_:1},8,["disabled"]),o(M,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:d(()=>[s(t).recentlySuccessful?(l(),i("p",se,"Saved.")):p("",!0)]),_:1})])])])],40,Z)])]),o(D,{show:_.value,onClose:w},{default:d(()=>[e("div",oe,[(l(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6 text-gray-500 absolute top-4 right-4 cursor-pointer hover:text-red-500",onClick:w},ne)),le,e("div",ie,[e("table",re,[de,c.tags.length===0?(l(),i("tr",ce,pe)):p("",!0),e("tbody",me,[(l(!0),i(k,null,B(c.tags,(n,f)=>(l(),i("tr",{key:f},[e("td",_e,[e("div",ve,[e("span",null,C(n.name),1),e("span",{onClick:xe=>j(n.name,f,"name"),class:"cursor-pointer"}," 📋 ",8,ge)]),v.value===f&&g.value==="name"?(l(),i("span",fe," Copied! ")):p("",!0)]),e("td",ye,[e("div",be,[e("span",null,C(n.description),1)])])]))),128))])])])])]),_:1},8,["show"])]),_:1})],64))}};export{Ee as default};
