<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MailConfig extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected static $logName = 'Mail Config';

    protected static $logAttributes = [
        'host',
        'port',
        'username',
        'email',
        'encryption',
        'name',
        'companyInfo',
    ];

    public function getLogDescription(string $event): string
    {
        return "<strong>Mail configuration '{$this->name}'</strong> has been {$event} by";
    }

    protected $fillable = [
        'host',
        'port',
        'username',
        'password',
        'email',
        'encryption',
        'name',
        'companyInfo',
    ];

    public function sequences()
    {
        return $this->hasMany(Sequences::class, 'smtp_id', 'id');
    }


}
