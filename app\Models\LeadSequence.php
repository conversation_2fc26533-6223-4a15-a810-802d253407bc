<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LeadSequence extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected static $logName = 'Lead Sequence';

    protected static $logAttributes = [
        'lead_id',
        'sequence_id',
        'current_step',
        'next_scheduled_at',
        'sent_time',
        'subject',
        'content',
        'full_content',
        'status',
        'opened_at',
        'clicked_at',
    ];

    public function getLogDescription(string $event): string
    {
        $leadName = $this->lead ? $this->lead->first_name . ' ' . $this->lead->last_name : 'Lead';
        return "<strong>Lead sequence for {$leadName}</strong> has been {$event} by";
    }

    protected $table = 'lead_sequences';

    protected $fillable = [
        'lead_id',
        'sequence_id',
        'current_step',
        'next_scheduled_at',
        'sent_time',
        'subject',
        'content',
        'full_content',
        'status',
        'opened_at',
        'clicked_at'
    ];

    public function lead()
    {
        return $this->belongsTo(Leads::class);
    }

    public function sequence()
    {
        return $this->belongsTo(Sequences::class);
    }
}
