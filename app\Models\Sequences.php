<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Sequences extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected static $logName = 'Sequence';

    protected static $logAttributes = [
        'name',
        'status',
        'smtp_id',
    ];

    public function getLogDescription(string $event): string
    {
        return "<strong>Email sequence '{$this->name}'</strong> has been {$event} by";
    }

    protected $table = 'sequences';

    protected $fillable = [
        'name',
        'status',
        'smtp_id',
    ];

    public function steps()
    {
        return $this->hasMany(SequenceStep::class, 'sequence_id', 'id');
    }

    public function mailConfig()
    {
        return $this->belongsTo(MailConfig::class, 'smtp_id');
    }
}
