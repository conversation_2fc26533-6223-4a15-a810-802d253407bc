<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailTag extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected static $logName = 'Email Tag';

    protected static $logAttributes = [
        'name',
        'description',
    ];

    public function getLogDescription(string $event): string
    {
        return "<strong>Email tag '{$this->name}'</strong> has been {$event} by";
    }

    protected $fillable = [
        'name',
        'description',
    ];

    public function sequenceSteps()
    {
        return $this->belongsToMany(SequenceStep::class, 'email_tag_sequence_step', 'email_tag_id', 'sequence_step_id');
    }
}
