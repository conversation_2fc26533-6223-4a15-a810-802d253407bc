import{T as h,o as n,c as m,a as o,u as s,w as c,F as u,Z as v,b as l,h as y,k as p,y as x,d as w,g as V,G as j,t as k}from"./app-78b9617f.js";import{_ as P,b as U}from"./AdminLayout-52ecaa4e.js";import{_ as a}from"./InputLabel-e6e35589.js";import{_ as d}from"./TextInput-f897977c.js";import{_ as i}from"./InputError-9ea1a4ce.js";import{P as $}from"./PrimaryButton-5ca54851.js";import"./_plugin-vue_export-helper-c27b6911.js";const D={class:"items-start"},S=l("div",{class:"flex justify-between items-center mb-6"},[l("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Portfolio Project")],-1),B={class:"bg-white rounded-lg shadow p-6"},N=["onSubmit"],E={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},T={class:"mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto border border-gray-300 rounded-md p-4"},C=["id","value"],L=["for"],M=l("p",{class:"mt-1 text-sm text-gray-500"},"Select all technologies used in this project",-1),A={class:"flex mt-6 items-center justify-between"},F={class:"ml-auto flex items-center justify-end gap-x-6"},I=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),K={__name:"Create",props:{technologies:Array},setup(g){const e=h({project_name:"",url:"",description:"",technology:[],login_id:"",password:""}),f=()=>{e.post(route("portfolios.store"),{onSuccess:()=>e.reset()})};return(_,r)=>(n(),m(u,null,[o(s(v),{title:"Portfolio"}),o(P,null,{default:c(()=>[l("div",D,[S,l("div",B,[l("form",{onSubmit:y(f,["prevent"]),class:"space-y-6"},[l("div",E,[l("div",null,[o(a,{for:"project_name",value:"Project Name *"}),o(d,{id:"project_name",modelValue:s(e).project_name,"onUpdate:modelValue":r[0]||(r[0]=t=>s(e).project_name=t),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",placeholder:"Enter project name"},null,8,["modelValue"]),o(i,{class:"mt-2",message:s(e).errors.project_name},null,8,["message"])]),l("div",null,[o(a,{for:"url",value:"Project URL"}),o(d,{id:"url",modelValue:s(e).url,"onUpdate:modelValue":r[1]||(r[1]=t=>s(e).url=t),type:"url",class:"mt-1 block w-full",placeholder:"https://example.com"},null,8,["modelValue"]),o(i,{class:"mt-2",message:s(e).errors.url},null,8,["message"])]),l("div",null,[o(a,{for:"description",value:"Description"}),p(l("textarea",{id:"description","onUpdate:modelValue":r[2]||(r[2]=t=>s(e).description=t),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Describe your project..."},null,512),[[x,s(e).description]]),o(i,{class:"mt-2",message:s(e).errors.description},null,8,["message"])]),l("div",null,[o(a,{for:"technology",value:"Technologies Used"}),l("div",T,[(n(!0),m(u,null,w(g.technologies,t=>(n(),m("div",{key:t.id,class:"flex items-center"},[p(l("input",{id:`tech-${t.id}`,"onUpdate:modelValue":r[3]||(r[3]=b=>s(e).technology=b),value:t.id,type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,8,C),[[j,s(e).technology]]),l("label",{for:`tech-${t.id}`,class:"ml-2 text-sm text-gray-700"},k(t.name),9,L)]))),128))]),M,o(i,{class:"mt-2",message:s(e).errors.technology},null,8,["message"])]),l("div",null,[o(a,{for:"login_id",value:"Login ID"}),o(d,{id:"login_id",modelValue:s(e).login_id,"onUpdate:modelValue":r[4]||(r[4]=t=>s(e).login_id=t),type:"text",class:"mt-1 block w-full",placeholder:"Enter login ID (optional)"},null,8,["modelValue"]),o(i,{class:"mt-2",message:s(e).errors.login_id},null,8,["message"])]),l("div",null,[o(a,{for:"password",value:"Password"}),o(d,{id:"password",modelValue:s(e).password,"onUpdate:modelValue":r[5]||(r[5]=t=>s(e).password=t),type:"text",class:"mt-1 block w-full",placeholder:"Enter password (optional)"},null,8,["modelValue"]),o(i,{class:"mt-2",message:s(e).errors.password},null,8,["message"])])]),l("div",A,[l("div",F,[o(U,{href:_.route("portfolios.index")},{svg:c(()=>[I]),_:1},8,["href"]),o($,{disabled:s(e).processing},{default:c(()=>[V("Save")]),_:1},8,["disabled"])])])],40,N)])])]),_:1})],64))}};export{K as default};
