<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Leads extends Model
{
    use HasFactory, ActivityTrait;

    protected static $logName = 'Lead';

    protected static $logAttributes = [
        'first_name',
        'last_name',
        'country',
        'city',
        'designation',
        'email',
        'organization_name',
        'organization_website_url',
        'linkedin_url',
        'organization_linkedin_url',
    ];

    public function getLogDescription(string $event): string
    {
        return "<strong>{$this->first_name} {$this->last_name}</strong> has been {$event} by";
    }

    protected $fillable = [
        'first_name',
        'last_name',
        'country',
        'city',
        'designation',
        'email',
        'organization_name',
        'organization_website_url',
        'linkedin_url',
        'organization_linkedin_url',
    ];

    public function sequences()
    {
        return $this->hasMany(LeadSequence::class, 'lead_id', 'id');
    }

    public function leadSequences()
    {
        return $this->hasMany(LeadSequence::class,'lead_id', 'id');
    }

}
