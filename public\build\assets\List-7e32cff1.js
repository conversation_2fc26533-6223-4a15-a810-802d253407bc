import{_ as S,a as V,b as j}from"./AdminLayout-52ecaa4e.js";import{_ as z}from"./CreateButton-ce8e809c.js";import{_ as b}from"./SecondaryButton-49ed53f4.js";import{D as L}from"./DangerButton-12a80368.js";import{M as v}from"./Modal-e411db4b.js";import{_ as E}from"./Pagination-38f0db12.js";import{_ as N}from"./InputLabel-e6e35589.js";import{_ as P}from"./TextInput-f897977c.js";import{r as y,T as k,o as a,c as i,a as e,u as d,w as s,F as h,Z as O,b as t,g as r,d as A,e as F,f as H,t as m}from"./app-78b9617f.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const U={class:"animate-top"},I={class:"sm:flex sm:items-center"},Z=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"SMTP")],-1),q={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},G={class:"flex justify-end"},J={class:"mt-8",style:{"min-height":"500px","margin-bottom":"80px"}},K={class:"p-1 bg-white shadow ring-1 overflow-x-auto ring-black ring-opacity-5 sm:rounded-lg"},Q={class:"min-w-full divide-y divide-gray-300"},R=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Host"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Port"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Username "),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Email"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Encryption"),t("th",{scope:"col",class:"px-3 py-3 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),W={key:0},X={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},Y={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},D={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},tt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},et={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},st={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},ot={class:"flex items-center justify-start gap-4"},at=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),lt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),nt=t("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),it=["onClick"],ct=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),rt=t("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),dt=[ct,rt],mt=["onClick"],pt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M3 8l7 5 7-5M3 8v8m14-8v8M3 8l7 5 7-5"})],-1),ht=t("span",{class:"text-sm text-gray-700 leading-5"},"Test Mail",-1),ut=[pt,ht],_t={class:"sm:col-span-2 p-4"},xt=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Send Test Mail",-1),ft={class:"mt-4 flex justify-end space-x-2"},gt=["disabled"],yt=t("svg",{class:"animate-spin h-4 w-4 mr-2 text-white inline-block",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"})],-1),wt={key:1},bt=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),vt=[bt],kt=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Mt={class:"p-6"},Ct=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),Tt={class:"mt-6 flex justify-end"},Ht={__name:"List",props:{data:{type:Object,default:()=>({data:[]})}},setup(l){l.data.id;const M=()=>{if(!n.email){alert("Please enter an email address.");return}n.post(route("smtp.sendTestMail",p.value),{onSuccess:()=>f()})},C=c=>{p.value=c,_.value=!0},u=()=>{_.value=!1},T=()=>{B.delete(route("smtp.destroy",{id:p.value}),{onSuccess:()=>u()})},_=y(!1),p=y(null),B=k({}),x=y(!1),n=k({email:""}),$=c=>{p.value=c,n.email="",x.value=!0},f=()=>{x.value=!1};return(c,w)=>(a(),i(h,null,[e(d(O),{title:"SMTP"}),e(S,null,{trigger:s(()=>[kt]),default:s(()=>[t("div",U,[t("div",I,[Z,t("div",q,[t("div",G,[e(z,{href:c.route("smtp.create")},{default:s(()=>[r("Create SMTP")]),_:1},8,["href"])])])]),t("div",J,[t("div",K,[t("table",Q,[R,l.data.data&&l.data.data.length?(a(),i("tbody",W,[(a(!0),i(h,null,A(l.data.data,(o,$t)=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:o.id},[t("td",X,m(o.host),1),t("td",Y,m(o.port),1),t("td",D,m(o.username),1),t("td",tt,m(o.email),1),t("td",et,m(o.encryption),1),t("td",st,[t("div",ot,[e(V,{align:"right",width:"48"},{trigger:s(()=>[at]),content:s(()=>[e(j,{href:c.route("smtp.edit",{id:o.id})},{svg:s(()=>[lt]),text:s(()=>[nt]),_:2},1032,["href"]),t("button",{type:"button",onClick:g=>C(o.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},dt,8,it),t("button",{type:"button",onClick:g=>$(o.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ut,8,mt),e(v,{show:x.value,onClose:f},{default:s(()=>[t("div",_t,[xt,e(N,{for:"email",value:"Email Address"}),e(P,{id:"email",type:"email",modelValue:d(n).email,"onUpdate:modelValue":w[0]||(w[0]=g=>d(n).email=g),placeholder:"Enter email address",class:"mt-1 block w-full",autocomplete:"email"},null,8,["modelValue"]),t("div",ft,[e(b,{onClick:f},{default:s(()=>[r("Cancel")]),_:1}),t("button",{onClick:M,disabled:d(n).processing,class:"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-white text-sm hover:bg-blue-700 disabled:opacity-50"},[d(n).processing?(a(),i(h,{key:0},[yt,r(" Sending... ")],64)):(a(),i(h,{key:1},[r("Send Email")],64))],8,gt)])])]),_:1},8,["show"])]),_:2},1024)])])]))),128))])):(a(),i("tbody",wt,vt))])]),l.data.data&&l.data.data.length>0?(a(),F(E,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):H("",!0)])]),e(v,{show:_.value,onClose:u},{default:s(()=>[t("div",Mt,[Ct,t("div",Tt,[e(b,{onClick:u},{default:s(()=>[r(" Cancel ")]),_:1}),e(L,{class:"ml-3",onClick:T},{default:s(()=>[r(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Ht as default};
