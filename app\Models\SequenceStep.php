<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SequenceStep extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected static $logName = 'Sequence Step';

    protected static $logAttributes = [
        'step_number',
        'sequence_id',
        'days_after_previous_step',
        'subject',
        'content',
    ];

    public function getLogDescription(string $event): string
    {
        return "<strong>Sequence step #{$this->step_number}</strong> has been {$event} by";
    }

    protected $fillable = [
        'step_number',
        'sequence_id',
        'days_after_previous_step',
        'subject',
        'content',
    ];

    public function sequence()
    {
        return $this->belongsTo(Sequences::class, 'email_sequence_id', 'id');
    }

    public function tags()
    {
        return $this->belongsToMany(EmailTag::class, 'email_tag_sequence_step', 'sequence_step_id', 'email_tag_id');
    }
}
