import{r as d,B as ce,C as ue,o,c as n,b as e,t as a,k as me,D as he,F as H,d as Q,n as V,f as u,T as Z,a as r,u as B,w as c,Z as pe,e as K,g as b,h as _e}from"./app-78b9617f.js";import{_ as ve,a as ge,b as xe}from"./AdminLayout-52ecaa4e.js";import{_ as G}from"./SecondaryButton-49ed53f4.js";import{D as fe}from"./DangerButton-12a80368.js";import{M as q}from"./Modal-e411db4b.js";import{_ as we}from"./Pagination-38f0db12.js";import{_ as ye}from"./SearchableDropdownNew-753797aa.js";import{_ as S}from"./InputLabel-e6e35589.js";import{_ as be}from"./TextInput-f897977c.js";import{_ as ke}from"./InputError-9ea1a4ce.js";import{P as Ce}from"./PrimaryButton-5ca54851.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const Me={class:""},Se={class:"flex items-start"},je={class:"block truncate"},Le={class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm cursor-pointer"},Be=["onClick","onMouseenter"],Ve=e("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z","clip-rule":"evenodd"})],-1),Te=[Ve],J={__name:"SimpleDropdown",props:["options","modelValue"],emits:["onchange"],setup(m,{emit:k}){const w=m,v=d({name:w.options[0].name,id:w.options[0].id}),f=d(!1),p=d(-1),C=()=>{f.value=!f.value},T=(h,g)=>{v.value={name:h,id:g},f.value=!1,k("onchange",g,h)},M=h=>{p.value=h};ce(()=>{const h=w.options.find(g=>g.id==w.modelValue);v.value=h,document.addEventListener("click",j)}),ue(()=>{document.removeEventListener("click",j)});const j=h=>{h.target.closest(".relative")||(f.value=!1)};return(h,g)=>(o(),n("div",Me,[e("button",{type:"button",class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-12 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",onClick:C},[e("div",Se,[e("span",je,a(v.value.name),1)])]),me(e("ul",Le,[(o(!0),n(H,null,Q(m.options,(s,_)=>(o(),n("li",{key:_,onClick:L=>T(s.name,s.id),onMouseenter:L=>M(_),class:V([{"text-white bg-indigo-600":p.value===_,"text-gray-900":p.value!==_},"relative cursor-default select-none py-2 pl-3 pr-9 cursor-pointer"]),tabindex:"-1",role:"option"},[e("span",{class:V(["block truncate",{"font-semibold":s.name===v.value.name}])},a(s.name),3),s.name===v.value.name?(o(),n("span",{key:0,class:V(["absolute inset-y-0 right-0 flex items-center pr-4",{"text-white":p.value===_,"text-indigo-600":p.value!==_}])},Te,2)):u("",!0)],42,Be))),128))],512),[[he,f.value]])]))}},$e={class:"animate-top"},Ee={class:"sm:flex sm:items-center"},ze=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Sent Emails")],-1),Oe={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},qe={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},Ie=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),Ae={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},He={class:"flex mb-2"},Ne=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Ue={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Fe={class:"sm:col-span-4"},Pe={class:"relative mt-2"},De={class:"sm:col-span-4"},Re={class:"relative mt-2"},Ze={class:"sm:col-span-4"},Ke={class:"relative mt-2"},Ge={class:"mt-8 overflow-x-auto sm:rounded-lg"},Je={class:"shadow sm:rounded-lg"},Qe={class:"w-full text-sm text-left rtl:text-right text-gray-500"},We=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Recipient"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Sequence"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Step"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Subject"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Next Schedule"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Sent At"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Status"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1),Xe={key:0},Ye={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},et={class:"text-xs text-blue-600"},tt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},st={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},ot={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},lt=["onClick"],nt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},at={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},it={key:0},rt={class:"text-xs text-gray-500"},dt={key:1},ct={class:"py-2.5"},ut={class:"flex justify-center items-center w-full"},mt={class:"items-center px-4 py-2.5"},ht={class:"flex items-center justify-start gap-4"},pt=e("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),_t=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),vt=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),gt=["onClick"],xt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),ft=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),wt=[xt,ft],yt=["onClick"],bt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"})],-1),kt=e("span",{class:"text-sm text-gray-700 leading-5"}," Reschedule ",-1),Ct=[bt,kt],Mt={key:1},St=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No emails found. ")],-1),jt=[St],Lt={class:"p-6"},Bt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),Vt={class:"mt-6 flex justify-end"},Tt={class:"p-0 max-h-[80vh] flex flex-col relative"},$t={class:"sticky top-0 bg-white z-10 px-4 py-3 border-b"},Et=e("h2",{class:"text-base font-medium text-gray-900"},"Email Content",-1),zt=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),Ot=[zt],qt={class:"px-4 pb-4 overflow-y-auto"},It=["innerHTML"],At={class:"p-0 max-h-[80vh] flex flex-col relative"},Ht={class:"sticky top-0 bg-white z-10 px-4 py-3 border-b"},Nt={class:"flex justify-between items-center"},Ut={class:"flex items-center"},Ft=e("h2",{class:"text-base font-medium text-gray-900"},"Email Preview",-1),Pt={key:0,class:"ml-3 bg-green-100 text-green-800 px-3 py-0.5 rounded-full text-xs font-medium"},Dt={key:1,class:"flex flex-col items-end"},Rt={class:"text-gray-500 text-xs ml-2"},Zt={class:"flex items-center space-x-4"},Kt=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),Gt=[Kt],Jt={key:0,class:"flex justify-center items-center p-6"},Qt=e("svg",{class:"animate-spin h-8 w-8 text-blue-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"3",opacity:"0.2"}),e("path",{stroke:"currentColor","stroke-linecap":"round","stroke-width":"3",d:"M22 12a10 10 0 0 0-20 0",opacity:"0.8"})],-1),Wt=[Qt],Xt={key:1,class:"px-4 pb-4 overflow-y-auto"},Yt={class:"bg-gray-100 p-3 mb-3 rounded text-sm mt-2"},es={class:"flex justify-between items-start"},ts={class:"space-y-2"},ss=e("span",{class:"text-gray-600 font-medium"},"From:",-1),os=e("span",{class:"text-gray-600 font-medium"},"To:",-1),ls=e("span",{class:"text-gray-600 font-medium"},"Subject:",-1),ns={key:0,class:"bg-green-50 border border-green-200 text-green-800 p-3 mb-3 rounded text-xs"},as={class:"flex items-start"},is=e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),rs=e("div",null,[e("p",{class:"font-medium"},"Viewing Completed Email"),e("p",null,"This preview shows the exact content that was sent to the recipient, including the company information as it appeared at the time of sending. Any changes made to templates or company information after completion are not reflected here.")],-1),ds={key:0,class:"border p-3 rounded bg-white"},cs=["innerHTML"],us={key:1,class:"border p-3 rounded bg-white"},ms=["innerHTML"],hs={key:2,class:"border p-3 rounded bg-white"},ps={key:0,class:"bg-green-50 border border-green-200 text-green-800 p-2 mb-3 rounded text-xs"},_s=e("div",{class:"flex items-start"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("div",null,[e("p",{class:"font-medium"},"Processed View"),e("p",null,"This shows how the email will look when sent.")])],-1),vs=[_s],gs=["innerHTML"],xs={key:3,class:"mt-3 border border-gray-200 rounded p-3 bg-gray-50"},fs=e("h3",{class:"text-xs font-medium text-gray-700 mb-2"},"Lead Data Used in Email:",-1),ws={class:"grid grid-cols-2 gap-2 text-xs"},ys=e("span",{class:"font-medium"},"Name:",-1),bs=e("span",{class:"font-medium"},"Email:",-1),ks=e("span",{class:"font-medium"},"Organization:",-1),Cs={key:2,class:"px-4 py-6 text-center"},Ms=e("div",{class:"text-red-500 mb-2"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-8 h-8 mx-auto"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"})])],-1),Ss={class:"text-gray-700 text-sm"},js={class:"p-0 max-h-[80vh] flex flex-col relative"},Ls={class:"sticky top-0 bg-white z-10 px-4 py-3 border-b"},Bs=e("h2",{class:"text-base font-medium text-gray-900"},"Edit Failed Email",-1),Vs=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),Ts=[Vs],$s={class:"px-6 py-4 overflow-y-auto"},Es=["onSubmit"],zs={class:"mb-4"},Os=e("p",{class:"mt-2 text-xs text-gray-500"}," Correct the email address to fix the delivery issue. ",-1),qs={key:0,class:"mb-4"},Is={class:"bg-gray-50 rounded-md p-4 border border-gray-200"},As={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},Hs=e("p",{class:"text-gray-500"},"Sequence:",-1),Ns={class:"font-medium"},Us=e("p",{class:"text-gray-500"},"Subject:",-1),Fs={class:"font-medium"},Ps=e("p",{class:"text-gray-500"},"Lead:",-1),Ds={class:"font-medium"},Rs=e("div",{class:"mb-4"},[e("div",{class:"bg-blue-50 border border-blue-200 rounded p-3 text-blue-800 text-sm"},[e("div",{class:"flex items-start"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("div",null,[e("p",{class:"font-medium"},"Automatic Rescheduling"),e("p",null,"This email will be automatically rescheduled for sending after updating.")])])])],-1),Zs={class:"flex items-center justify-between"},Ks={class:"ml-auto flex items-center justify-end space-x-4 mt-6"},io={__name:"List",props:{data:Object,sequences:Array,status:Object,filters:Object},setup(m){var F,P,D,R;const k=m,w=Z({}),v=d(!1),f=d(null),p=d(((F=k.filters)==null?void 0:F.sequence_id)||null),C=d(((P=k.filters)==null?void 0:P.status)||null),T=d(((D=k.filters)==null?void 0:D.search)||""),M=d(((R=k.filters)==null?void 0:R.scheduled_date)||null),j=d(!1),h=d(""),g=d(!1),s=d(null),_=d(!1),L=d(!1),W=[{id:null,name:"All Dates"},{id:"today",name:"Today"}],X=l=>{switch(l){case"pending":return"bg-blue-100";case"In-process":return"bg-yellow-100";case"completed":return"bg-green-100";default:return"bg-gray-100"}},Y=l=>{switch(l){case"pending":return"text-blue-600";case"In-process":return"text-yellow-600";case"completed":return"text-green-600";default:return"text-gray-600"}},ee=l=>{f.value=l,v.value=!0},I=()=>{v.value=!1},N=()=>{j.value=!1},U=()=>{g.value=!1,s.value=null,L.value=!1},te=async l=>{_.value=!0,g.value=!0;try{const t=await(await fetch(route("email.preview",l))).json();s.value=t}catch(i){console.error("Error fetching email preview:",i),s.value={success:!1,message:"Failed to load preview. Please try again."}}finally{_.value=!1}},se=l=>{p.value=l,$()},oe=l=>{C.value=l,$()},le=l=>{M.value=l,$()},$=l=>{l!==void 0&&(T.value=l),w.get(route("sent-email.index",{search:T.value,sequence_id:p.value,status:C.value,scheduled_date:M.value}),{preserveState:!0})},ne=()=>{w.delete(route("sent-email.destroy",{id:f.value}),{onSuccess:()=>I()})},ae=l=>{const i=new Date(l),t={year:"numeric",month:"short",day:"numeric"};return i.toLocaleDateString("en-US",t)},E=l=>{if(!l)return{time:"-",date:"-"};const i=l.split(/[- :]/),t=new Date(Date.UTC(parseInt(i[0]),parseInt(i[1])-1,parseInt(i[2]),parseInt(i[3]),parseInt(i[4]),parseInt(i[5]))),O=t.toLocaleTimeString("en-IN",{hour:"2-digit",minute:"2-digit",hour12:!0,timeZone:"Asia/Kolkata"}),de=t.toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric",timeZone:"Asia/Kolkata"});return{time:O,date:de}},y=d(null);d(null);const A=d(!1),x=Z({id:null,email:"",reschedule:!0}),ie=l=>{y.value=l,x.id=l.id,x.email=l.lead.email,x.reschedule=!0,A.value=!0},z=()=>{A.value=!1,x.reset(),y.value=null},re=()=>{x.patch(route("failed-emails.update"),{onSuccess:l=>{z()}})};return(l,i)=>(o(),n(H,null,[r(B(pe),{title:"Sent Emails"}),r(ve,null,{default:c(()=>[e("div",$e,[e("div",Ee,[ze,e("div",Oe,[r(S,{for:"search_field"}),e("div",qe,[Ie,e("input",{id:"search-field",onInput:i[0]||(i[0]=t=>$(t.target.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])])]),e("div",Ae,[e("div",He,[Ne,r(S,{for:"customer_id",value:"Filters"})]),e("div",Ue,[e("div",Fe,[r(S,{for:"sequence_id",value:"Sequence"}),e("div",Pe,[r(ye,{options:[{id:"",name:"All Sequence"},...m.sequences],modelValue:p.value,"onUpdate:modelValue":i[1]||(i[1]=t=>p.value=t),onOnchange:i[2]||(i[2]=t=>se(t))},null,8,["options","modelValue"])])]),e("div",De,[r(S,{for:"status_id",value:"Status"}),e("div",Re,[r(J,{options:m.status,modelValue:C.value,"onUpdate:modelValue":i[3]||(i[3]=t=>C.value=t),onOnchange:oe},null,8,["options","modelValue"])])]),e("div",Ze,[r(S,{for:"scheduled_date",value:"Date"}),e("div",Ke,[r(J,{options:W,modelValue:M.value,"onUpdate:modelValue":i[4]||(i[4]=t=>M.value=t),onOnchange:le},null,8,["modelValue"])])])])]),e("div",Ge,[e("div",Je,[e("table",Qe,[We,m.data.data&&m.data.data.length>0?(o(),n("tbody",Xe,[(o(!0),n(H,null,Q(m.data.data,t=>(o(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ye,[e("div",null,a(t.lead.first_name??"-")+" "+a(t.lead.last_name??"-"),1),e("div",et,a(t.lead.email??"-"),1)]),e("td",tt,a(t.sequence.name??"-"),1),e("td",st,a(t.current_step??"-"),1),e("td",ot,[e("button",{onClick:O=>te(t.id)},a(t.subject||"-"),9,lt)]),e("td",nt,a(ae(t.next_scheduled_at)??"-"),1),e("td",at,[t.sent_time?(o(),n("div",it,[e("div",null,a(E(t.sent_time).time),1),e("div",rt,a(E(t.sent_time).date),1)])):(o(),n("div",dt,"-"))]),e("td",ct,[e("div",ut,[e("div",{class:V(["flex rounded-full px-4 py-1",X(t.status)])},[e("span",{class:V(["text-sm font-semibold",Y(t.status)])},a(t.status),3)],2)])]),e("td",mt,[e("div",ht,[r(ge,{align:"right",width:"48"},{trigger:c(()=>[pt]),content:c(()=>[t.status=="pending"?(o(),K(xe,{key:0,href:l.route("sent-email.edit",{id:t.id})},{svg:c(()=>[_t]),text:c(()=>[vt]),_:2},1032,["href"])):u("",!0),t.status=="pending"?(o(),n("button",{key:1,type:"button",onClick:O=>ee(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},wt,8,gt)):u("",!0),t.status=="completed"?(o(),n("button",{key:2,type:"button",onClick:O=>ie(t),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ct,8,yt)):u("",!0)]),_:2},1024)])])]))),128))])):(o(),n("tbody",Mt,jt))])])]),m.data.data&&m.data.data.length>0?(o(),K(we,{key:0,class:"mt-6",links:m.data.links},null,8,["links"])):u("",!0)]),r(q,{show:v.value,onClose:I},{default:c(()=>[e("div",Lt,[Bt,e("div",Vt,[r(G,{onClick:I},{default:c(()=>[b(" Cancel ")]),_:1}),r(fe,{class:"ml-3",onClick:ne},{default:c(()=>[b(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(q,{show:j.value,onClose:N,"max-width":"2xl"},{default:c(()=>[e("div",Tt,[e("div",$t,[Et,(o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500",onClick:N},Ot))]),e("div",qt,[e("div",{class:"prose prose-sm",innerHTML:h.value||"-"},null,8,It)])])]),_:1},8,["show"]),r(q,{show:g.value,onClose:U,"max-width":"2xl"},{default:c(()=>[e("div",At,[e("div",Ht,[e("div",Nt,[e("div",Ut,[Ft,s.value&&s.value.isSent?(o(),n("div",Pt," Completed ")):u("",!0),s.value&&s.value.isSent?(o(),n("div",Dt,[e("div",Rt," At "+a(E(s.value.sentTime).time)+" on "+a(E(s.value.sentTime).date)+". ",1)])):u("",!0)]),e("div",Zt,[(o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5 cursor-pointer text-gray-500 hover:text-red-500",onClick:U},Gt))])])]),_.value?(o(),n("div",Jt,Wt)):s.value&&s.value.success?(o(),n("div",Xt,[e("div",Yt,[e("div",es,[e("div",ts,[e("div",null,[ss,e("span",null,a(s.value.from.name)+" <"+a(s.value.from.email)+">",1)]),e("div",null,[os,e("span",null,a(s.value.recipient),1)]),e("div",null,[ls,e("span",null,a(s.value.subject),1)])])])]),s.value.isSent?(o(),n("div",ns,[e("div",as,[is,rs,L.value&&!s.value.isSent?(o(),n("div",ds,[e("div",{class:"prose prose-sm max-w-none",innerHTML:s.value.originalContent},null,8,cs)])):u("",!0)])])):u("",!0),L.value&&!s.value.isSent?(o(),n("div",us,[e("div",{class:"prose prose-sm max-w-none",innerHTML:s.value.originalContent},null,8,ms)])):(o(),n("div",hs,[s.value.isSent?u("",!0):(o(),n("div",ps,vs)),e("div",{class:"prose prose-sm max-w-none",innerHTML:s.value.content},null,8,gs)])),s.value.lead?(o(),n("div",xs,[fs,e("div",ws,[e("div",null,[ys,b(" "+a(s.value.lead.first_name)+" "+a(s.value.lead.last_name),1)]),e("div",null,[bs,b(" "+a(s.value.lead.email),1)]),e("div",null,[ks,b(" "+a(s.value.lead.organization),1)])])])):u("",!0)])):s.value&&!s.value.success?(o(),n("div",Cs,[Ms,e("p",Ss,a(s.value.message),1)])):u("",!0)])]),_:1},8,["show"]),r(q,{show:A.value,onClose:z,"max-width":"md"},{default:c(()=>[e("div",js,[e("div",Ls,[Bs,(o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500",onClick:z},Ts))]),e("div",$s,[e("form",{onSubmit:_e(re,["prevent"])},[e("div",zs,[r(S,{for:"email",value:"Email Address",class:"mb-1"}),r(be,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:B(x).email,"onUpdate:modelValue":i[5]||(i[5]=t=>B(x).email=t),required:"",autocomplete:"email"},null,8,["modelValue"]),r(ke,{class:"mt-2",message:B(x).errors.email},null,8,["message"]),Os]),y.value?(o(),n("div",qs,[e("div",Is,[e("div",As,[e("div",null,[Hs,e("p",Ns,a(y.value.sequence.name),1)]),e("div",null,[Us,e("p",Fs,a(y.value.subject),1)]),e("div",null,[Ps,e("p",Ds,a(y.value.lead.first_name)+" "+a(y.value.lead.last_name),1)])])])])):u("",!0),Rs,e("div",Zs,[e("div",Ks,[r(G,{type:"button",onClick:z},{default:c(()=>[b("Cancel")]),_:1}),r(Ce,{type:"submit",disabled:B(x).processing},{default:c(()=>[b("Update")]),_:1},8,["disabled"])])])],40,Es)])])]),_:1},8,["show"])]),_:1})],64))}};export{io as default};
