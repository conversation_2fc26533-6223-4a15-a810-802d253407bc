<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Portfolio extends Model
{
    use HasFactory, SoftDeletes, ActivityTrait;

    protected static $logName = 'Portfolio';

    protected static $logAttributes = [
        'project_name',
        'url',
        'description',
        'technology',
        'login_id',
        'password',
    ];

    public function getLogDescription(string $event): string
    {
        return "<strong>Portfolio project '{$this->project_name}'</strong> has been {$event} by";
    }

    protected $fillable = [
        'project_name',
        'url',
        'description',
        'technology',
        'login_id',
        'password',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'technology' => 'array',
    ];

    // Accessors
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('M d, Y');
    }

    public function getFormattedUpdatedAtAttribute()
    {
        return $this->updated_at->format('M d, Y');
    }

    // Scopes
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('project_name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhereJsonContains('technology', $search)
              ->orWhere('technology', 'like', "%{$search}%"); // For backward compatibility
        });
    }

    public function scopeOrderByName($query, $direction = 'asc')
    {
        return $query->orderBy('project_name', $direction);
    }

    public function scopeOrderByDate($query, $direction = 'desc')
    {
        return $query->orderBy('created_at', $direction);
    }
}
