import{o as l,c as m,a as o,u as t,w as r,F as v,Z as x,b as e,h,e as b,f as d,n as g,A as y,g as c}from"./app-78b9617f.js";import{_ as w,b as S}from"./AdminLayout-52ecaa4e.js";import{_ as V}from"./InputError-9ea1a4ce.js";import{_ as u}from"./InputLabel-e6e35589.js";import{P as k}from"./PrimaryButton-5ca54851.js";import{_ as C}from"./TextInput-f897977c.js";import{_ as $}from"./SearchableDropdown-13422950.js";import{u as B}from"./index-322a58be.js";import"./_plugin-vue_export-helper-c27b6911.js";const N={class:"animate-top max-w-4xl mx-auto"},q={class:"bg-white rounded-xl shadow-sm border border-gray-200"},A=e("div",{class:"p-6 border-b border-gray-200"},[e("div",{class:"flex items-center gap-3"},[e("div",null,[e("h2",{class:"text-xl font-bold text-gray-900"},"Create New Sequence")])])],-1),F=["onSubmit"],M={class:"grid grid-cols-6 gap-6"},P={class:"col-span-3"},T={class:"mt-1 relative rounded-md shadow-sm"},j={class:"col-span-3"},U={class:"relative mt-2"},z={class:"mt-10 pt-6 border-t border-gray-200 flex items-center justify-between"},E={class:"flex items-center space-x-3"},L={key:0,class:"flex items-center text-sm text-green-600"},O=e("svg",{class:"w-4 h-4 mr-1.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),Z={class:"flex items-center space-x-3"},D=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Y={__name:"Add",props:{smtp:Array},setup(_){const s=B("post","/email-sequence",{name:"",smtp_id:""}),p=(i,a)=>{s.smtp_id=i,s.errors.smtp_id=null},f=()=>s.submit({preserveScroll:!0,onSuccess:()=>s.reset()});return(i,a)=>(l(),m(v,null,[o(t(x),{title:"Create Sequence"}),o(w,null,{default:r(()=>[e("div",N,[e("div",q,[A,e("form",{onSubmit:h(f,["prevent"]),class:"p-6"},[e("div",M,[e("div",P,[o(u,{value:"Sequence Name"}),e("div",T,[o(C,{id:"name",type:"text",modelValue:t(s).name,"onUpdate:modelValue":a[0]||(a[0]=n=>t(s).name=n),onChange:a[1]||(a[1]=n=>t(s).validate("name"))},null,8,["modelValue"])]),t(s).invalid("name")?(l(),b(V,{key:0,class:"mt-2",message:t(s).errors.name},null,8,["message"])):d("",!0)]),e("div",j,[o(u,{for:"smtp_id",value:"SMTP"}),e("div",U,[o($,{options:_.smtp,modelValue:t(s).smtp_id,"onUpdate:modelValue":a[2]||(a[2]=n=>t(s).smtp_id=n),onOnchange:p,class:g({"error rounded-md":t(s).errors.smtp_id})},null,8,["options","modelValue","class"])])])]),e("div",z,[e("div",E,[o(y,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:r(()=>[t(s).recentlySuccessful?(l(),m("div",L,[O,c(" Saved successfully ")])):d("",!0)]),_:1})]),e("div",Z,[o(S,{href:i.route("email-sequence.index")},{svg:r(()=>[D]),_:1},8,["href"]),o(k,{disabled:t(s).processing,class:"inline-flex items-center px-4 py-2"},{default:r(()=>[c(" Submit ")]),_:1},8,["disabled"])])])],40,F)])])]),_:1})],64))}};export{Y as default};
