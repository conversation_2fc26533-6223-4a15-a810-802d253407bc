import{_ as o}from"./AdminLayout-52ecaa4e.js";import i from"./DeleteUserForm-2e0c428e.js";import m from"./UpdatePasswordForm-6da90e19.js";import r from"./UpdateProfileInformationForm-02dbc9c6.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-78b9617f.js";import"./DangerButton-12a80368.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-9ea1a4ce.js";import"./InputLabel-e6e35589.js";import"./Modal-e411db4b.js";/* empty css                                                              */import"./SecondaryButton-49ed53f4.js";import"./TextInput-f897977c.js";import"./PrimaryButton-5ca54851.js";import"./TextArea-11caf175.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
