import{K as g,T as p,o as _,c,a as e,u as a,w as m,F as f,Z as y,b as l,h as v,g as V,A as z,f as b}from"./app-78b9617f.js";import{_ as k,b as x}from"./AdminLayout-52ecaa4e.js";import{_ as i}from"./InputError-9ea1a4ce.js";import{_ as r}from"./InputLabel-e6e35589.js";import{P as w}from"./PrimaryButton-5ca54851.js";import{_ as d}from"./TextInput-f897977c.js";import"./_plugin-vue_export-helper-c27b6911.js";const U={class:"animate-top"},L={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},h=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Leads",-1),N={class:"border-b border-gray-900/10 pb-12"},$={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},C={class:"sm:col-span-2"},B={class:"sm:col-span-2"},E={class:"sm:col-span-2"},O={class:"sm:col-span-2"},j={class:"sm:col-span-2"},F={class:"sm:col-span-2"},R={class:"sm:col-span-2"},S={class:"sm:col-span-3"},T={class:"sm:col-span-3"},D={class:"sm:col-span-3"},P={class:"flex mt-6 items-center justify-between"},q={class:"ml-auto flex items-center justify-end gap-x-6"},A=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),K={key:0,class:"text-sm text-gray-600"},X={__name:"Edit",props:{data:{type:Object}},setup(M){const n=g().props.data,s=p({first_name:n.first_name,last_name:n.last_name,country:n.country,city:n.city,name:n.name,designation:n.designation,email:n.email,organization_name:n.organization_name,organization_website_url:n.organization_website_url,linkedin_url:n.linkedin_url,organization_linkedin_url:n.organization_linkedin_url,id:n.id});return(u,t)=>(_(),c(f,null,[e(a(y),{title:"Leads"}),e(k,null,{default:m(()=>[l("div",U,[l("div",L,[h,l("form",{onSubmit:t[10]||(t[10]=v(o=>a(s).patch(u.route("leads.update")),["prevent"]))},[l("div",N,[l("div",$,[l("div",C,[e(r,{for:"first_name",value:"First Name"}),e(d,{id:"first_name",type:"text",modelValue:a(s).first_name,"onUpdate:modelValue":t[0]||(t[0]=o=>a(s).first_name=o),autocomplete:"first_name"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.first_name},null,8,["message"])]),l("div",B,[e(r,{for:"last_name",value:"Last Name"}),e(d,{id:"last_name",type:"text",modelValue:a(s).last_name,"onUpdate:modelValue":t[1]||(t[1]=o=>a(s).last_name=o),autocomplete:"last_name"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.last_name},null,8,["message"])]),l("div",E,[e(r,{for:"country",value:"Country"}),e(d,{id:"country",type:"text",modelValue:a(s).country,"onUpdate:modelValue":t[2]||(t[2]=o=>a(s).country=o),autocomplete:"country"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.country},null,8,["message"])]),l("div",O,[e(r,{for:"city",value:"City"}),e(d,{id:"city",type:"text",modelValue:a(s).city,"onUpdate:modelValue":t[3]||(t[3]=o=>a(s).city=o),autocomplete:"city"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.city},null,8,["message"])]),l("div",j,[e(r,{for:"designation",value:"Designation"}),e(d,{id:"designation",type:"text",modelValue:a(s).designation,"onUpdate:modelValue":t[4]||(t[4]=o=>a(s).designation=o),autocomplete:"designation"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.designation},null,8,["message"])]),l("div",F,[e(r,{for:"email",value:"Email"}),e(d,{id:"email",type:"email",modelValue:a(s).email,"onUpdate:modelValue":t[5]||(t[5]=o=>a(s).email=o),required:"",autocomplete:"username"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.email},null,8,["message"])]),l("div",R,[e(r,{for:"organization_name",value:"Organization Name"}),e(d,{id:"organization_name",type:"text",modelValue:a(s).organization_name,"onUpdate:modelValue":t[6]||(t[6]=o=>a(s).organization_name=o),autocomplete:"organization_name"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.organization_name},null,8,["message"])]),l("div",S,[e(r,{for:"organization_website_url",value:"Organization website URL"}),e(d,{id:"organization_website_url",type:"url",modelValue:a(s).organization_website_url,"onUpdate:modelValue":t[7]||(t[7]=o=>a(s).organization_website_url=o),autocomplete:"organization_website_url"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.organization_website_url},null,8,["message"])]),l("div",T,[e(r,{for:"linkedin_url",value:"Linkedin URL"}),e(d,{id:"linkedin_url",type:"url",modelValue:a(s).linkedin_url,"onUpdate:modelValue":t[8]||(t[8]=o=>a(s).linkedin_url=o),autocomplete:"linkedin_url"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.linkedin_url},null,8,["message"])]),l("div",D,[e(r,{for:"organization_linkedin_url",value:"Organization Linkedin URL"}),e(d,{id:"organization_linkedin_url",type:"url",modelValue:a(s).organization_linkedin_url,"onUpdate:modelValue":t[9]||(t[9]=o=>a(s).organization_linkedin_url=o),autocomplete:"organization_linkedin_url"},null,8,["modelValue"]),e(i,{class:"",message:a(s).errors.organization_linkedin_url},null,8,["message"])])])]),l("div",P,[l("div",q,[e(x,{href:u.route("leads.index")},{svg:m(()=>[A]),_:1},8,["href"]),e(w,{disabled:a(s).processing},{default:m(()=>[V("Update")]),_:1},8,["disabled"]),e(z,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:m(()=>[a(s).recentlySuccessful?(_(),c("p",K,"Saved.")):b("",!0)]),_:1})])])],32)])])]),_:1})],64))}};export{X as default};
