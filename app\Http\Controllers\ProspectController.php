<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Prospect;
use App\Models\ProspectActivity;
use App\Models\User;
use App\Models\Leads;
use App\Http\Requests\ProspectStoreRequest;
use App\Http\Requests\ProspectUpdateRequest;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Carbon\Carbon;

class ProspectController extends Controller
{
    public function index(Request $request)
    {
        // dd('hi');
        $search = $request->input('search');
        $status = $request->input('status');
        $priority = $request->input('priority');
        $leadSource = $request->input('lead_source');
        $assignedTo = $request->input('assigned_to');

        $query = Prospect::with(['assignedUser', 'convertedLead']);

        // Search functionality
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($status) {
            $query->byStatus($status);
        }

        if ($priority) {
            $query->byPriority($priority);
        }

        if ($leadSource) {
            $query->byLeadSource($leadSource);
        }

        if ($assignedTo) {
            $query->assignedTo($assignedTo);
        }

        $status = ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'];
        $query->orderByRaw("FIELD(status, '".implode("','", $status)."')");
        $prospects = $query->orderBy('created_at', 'desc')->paginate(5);

        // Get filter options
        $users = User::select('id', 'first_name as name')->get();
        $statusOptions = ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'];
        $priorityOptions = ['low', 'medium', 'high', 'urgent'];
        $leadSourceOptions = ['linkedin', 'upwork_jignesh', 'upwork_abhishek', 'email', 'referral', 'website', 'cold_call', 'social_media', 'other'];

        return Inertia::render('Prospects/List', [
            'prospects' => $prospects,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'priority' => $priority,
                'lead_source' => $leadSource,
                'assigned_to' => $assignedTo,
            ],
            'filterOptions' => [
                'users' => $users,
                'statuses' => $statusOptions,
                'priorities' => $priorityOptions,
                'leadSources' => $leadSourceOptions,
            ]
        ]);
    }

    public function create()
    {
        $users = User::select('id', 'first_name as name')->get();
        $statusOptions = ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'];
        $priorityOptions = ['low', 'medium', 'high', 'urgent'];
        $leadSourceOptions = ['linkedin', 'upwork_jignesh', 'upwork_abhishek', 'email', 'referral', 'website', 'cold_call', 'social_media', 'other'];
        $budgetRangeOptions = ['under_1k', '1k_5k', '5k_10k', '10k_25k', '25k_50k', 'over_50k'];

        return Inertia::render('Prospects/Add', [
            'users' => $users,
            'statusOptions' => $statusOptions,
            'priorityOptions' => $priorityOptions,
            'leadSourceOptions' => $leadSourceOptions,
            'budgetRangeOptions' => $budgetRangeOptions,
        ]);
    }

    public function store(ProspectStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $prospect = Prospect::create($request->validated());

            // Add initial activity
            $prospect->addActivity(
                'note_added',
                'Prospect created',
                'Initial prospect entry created in the system'
            );

            DB::commit();
            return Redirect::route('prospects.index')->with('success', 'Prospect created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::route('prospects.index')->with('error', 'Failed to create prospect: ' . $e->getMessage());
        }
    }

    public function show($id)
    {
        $prospect = Prospect::with(['assignedUser', 'convertedLead', 'activities.user'])
                           ->findOrFail($id);

        return Inertia::render('Prospects/Show', [
            'prospect' => $prospect,
        ]);
    }

    public function edit($id)
    {
        $prospect = Prospect::findOrFail($id);
        $users = User::select('id', 'first_name as name')->get();
        $statusOptions = ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'];
        $priorityOptions = ['low', 'medium', 'high', 'urgent'];
        $leadSourceOptions = ['linkedin', 'upwork_jignesh', 'upwork_abhishek', 'email', 'referral', 'website', 'cold_call', 'social_media', 'other'];
        $budgetRangeOptions = ['under_1k', '1k_5k', '5k_10k', '10k_25k', '25k_50k', 'over_50k'];

        return Inertia::render('Prospects/Edit', [
            'prospect' => $prospect,
            'users' => $users,
            'statusOptions' => $statusOptions,
            'priorityOptions' => $priorityOptions,
            'leadSourceOptions' => $leadSourceOptions,
            'budgetRangeOptions' => $budgetRangeOptions,
        ]);
    }

    public function update(ProspectUpdateRequest $request, $id)
    {
        $prospect = Prospect::findOrFail($id);

        DB::beginTransaction();
        try {
            $oldStatus = $prospect->status;
            $prospect->update($request->validated());

            // Track status change
            if ($oldStatus !== $request->status) {
                $prospect->addActivity(
                    'status_changed',
                    "Status changed from {$oldStatus} to {$request->status}",
                    $request->notes
                );
            }

            DB::commit();
            return Redirect::route('prospects.index')->with('success', 'Prospect updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::route('prospects.index')->with('error', 'Failed to update prospect: ' . $e->getMessage());
        }
    }

    public function destroy($id)
    {
        try {
            $prospect = Prospect::findOrFail($id);
            $prospect->delete();

            return Redirect::route('prospects.index')->with('success', 'Prospect deleted successfully.');
        } catch (\Exception $e) {
            return Redirect::route('prospects.index')->with('error', 'Failed to delete prospect: ' . $e->getMessage());
        }
    }

    // Prospect-specific methods
    public function addActivity(Request $request, $id)
    {
        $prospect = Prospect::findOrFail($id);

        $request->validate([
            'activity_type' => 'required|in:email_sent,email_received,call_made,call_received,meeting_scheduled,meeting_completed,note_added,status_changed,linkedin_message,proposal_sent,follow_up_scheduled,document_shared,other',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'metadata' => 'nullable|array',
            'activity_date' => 'nullable|date',
            'schedule_followup' => 'nullable|boolean',
            'followup_date' => 'nullable|date|after_or_equal:today',
        ]);

        try {


            // Handle follow-up scheduling
            if ($request->schedule_followup && $request->followup_date) {
                $prospect->update([
                    'next_follow_up_at' => $request->followup_date
                ]);
                $prospect->addActivity(
                    'follow_up_scheduled',
                    'Follow-up scheduled for ' . Carbon::parse($request->followup_date)->format('M d, Y'),
                    $request->notes
                );
            }

            $prospect->addActivity(
                $request->activity_type,
                $request->title,
                $request->description,
                $request->metadata,
                auth()->id(),
                $request->activity_date
            );

            return Redirect::back()->with('success', 'Activity added successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to add activity: ' . $e->getMessage());
        }
    }

    public function convertToLead($id)
    {
        $prospect = Prospect::findOrFail($id);

        if ($prospect->status === 'converted') {
            return Redirect::back()->with('error', 'Prospect is already converted to a lead.');
        }

        try {
            $lead = $prospect->convertToLead();

            return Redirect::route('leads.show', $lead->id)
                          ->with('success', 'Prospect successfully converted to lead.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to convert prospect: ' . $e->getMessage());
        }
    }

    public function updateStatus(Request $request, $id)
    {
        $prospect = Prospect::findOrFail($id);

        $request->validate([
            'status' => 'required|in:new,contacted,qualified,unqualified,converted,lost',
            'notes' => 'nullable|string',
        ]);

        try {
            $prospect->updateStatus($request->status, $request->notes);

            return Redirect::back()->with('success', 'Prospect status updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to update status: ' . $e->getMessage());
        }
    }

    public function scheduleFollowUp(Request $request, $id)
    {
        $prospect = Prospect::findOrFail($id);

        $request->validate([
            'next_follow_up_at' => 'required|date|after:now',
            'notes' => 'nullable|string',
        ]);

        try {
            $prospect->update([
                'next_follow_up_at' => $request->next_follow_up_at,
            ]);

            $prospect->addActivity(
                'follow_up_scheduled',
                'Follow-up scheduled for ' . Carbon::parse($request->next_follow_up_at)->format('M d, Y'),
                $request->notes
            );

            return Redirect::back()->with('success', 'Follow-up scheduled successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to schedule follow-up: ' . $e->getMessage());
        }
    }

    public function dashboard()
    {
        $stats = [
            'total_prospects' => Prospect::count(),
            'new_prospects' => Prospect::byStatus('new')->count(),
            'qualified_prospects' => Prospect::byStatus('qualified')->count(),
            'converted_prospects' => Prospect::byStatus('converted')->count(),
            'overdue_follow_ups' => Prospect::needFollowUp()->count(),
        ];

        $recentProspects = Prospect::with('assignedUser')
                                 ->orderBy('created_at', 'desc')
                                 ->limit(10)
                                 ->get();

        $overdueFollowUps = Prospect::with('assignedUser')
                                  ->needFollowUp()
                                  ->orderBy('next_follow_up_at', 'asc')
                                  ->limit(10)
                                  ->get();

        return Inertia::render('Prospects/Dashboard', [
            'stats' => $stats,
            'recentProspects' => $recentProspects,
            'overdueFollowUps' => $overdueFollowUps,
        ]);
    }
}
