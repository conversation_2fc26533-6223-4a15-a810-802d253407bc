import{K as u,T as y,o as p,c as _,a as o,u as s,w as i,F as b,Z as w,b as e,j as f,t as h,h as k,n as S,A as V,g as l,f as C}from"./app-78b9617f.js";import{_ as q}from"./AdminLayout-52ecaa4e.js";import{_ as B}from"./InputError-9ea1a4ce.js";import{_ as g}from"./InputLabel-e6e35589.js";import{P as $}from"./PrimaryButton-5ca54851.js";import{_ as j}from"./TextInput-f897977c.js";import{_ as D}from"./SearchableDropdown-13422950.js";import"./_plugin-vue_export-helper-c27b6911.js";const N={class:"animate-top max-w-4xl mx-auto"},E={class:"bg-white rounded-xl shadow-sm border border-gray-200"},M={class:"p-6 border-b border-gray-200"},T={class:"flex items-center gap-3"},L=e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),P=e("h2",{class:"text-xl font-bold text-gray-900"},"Edit Sequence",-1),U={class:"text-sm text-gray-500 mt-1"},A={class:"mr-4"},F={class:"grid grid-cols-6 gap-6"},O={class:"col-span-3"},z={class:"mt-1 relative rounded-md shadow-sm"},K={class:"col-span-3"},Z={class:"relative mt-2"},G={class:"mt-10 pt-6 border-t border-gray-200 flex items-center justify-between"},H={class:"flex items-center space-x-3"},I={key:0,class:"flex items-center text-sm text-green-600"},J=e("svg",{class:"w-4 h-4 mr-1.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),Q={class:"flex items-center space-x-3"},ae={__name:"Edit",props:{data:{type:Object},smtp:{type:Array}},setup(c){const d=u().props.data,x=u().props.smtp,v=(r,a)=>{t.smtp_id=r,t.errors.smtp_id=null},m=r=>{const a=new Date(r),n={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",n)},t=y({name:d.name,id:d.id,smtp_id:d.smtp_id});return(r,a)=>(p(),_(b,null,[o(s(w),{title:"Edit Sequence"}),o(q,null,{default:i(()=>[e("div",N,[e("div",E,[e("div",M,[e("div",T,[o(s(f),{href:r.route("email-sequence.index"),class:"text-gray-400 hover:text-gray-500"},{default:i(()=>[L]),_:1},8,["href"]),e("div",null,[P,e("div",U,[e("span",A,"Created: "+h(m(c.data.created_at)),1),e("span",null,"Last updated: "+h(m(c.data.updated_at)),1)])])])]),e("form",{onSubmit:a[2]||(a[2]=k(n=>s(t).patch(r.route("email-sequence.update")),["prevent"])),class:"p-6"},[e("div",F,[e("div",O,[o(g,{value:"Sequence Name",class:"block text-sm font-medium text-gray-700 mb-1"}),e("div",z,[o(j,{id:"name",type:"text",modelValue:s(t).name,"onUpdate:modelValue":a[0]||(a[0]=n=>s(t).name=n),class:"block w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"},null,8,["modelValue"])]),o(B,{class:"mt-2",message:s(t).errors.name},null,8,["message"])]),e("div",K,[o(g,{for:"smtp_id",value:"SMTP"}),e("div",Z,[o(D,{options:s(x),modelValue:s(t).smtp_id,"onUpdate:modelValue":a[1]||(a[1]=n=>s(t).smtp_id=n),onOnchange:v,class:S({"error rounded-md":s(t).errors.smtp_id})},null,8,["options","modelValue","class"])])])]),e("div",G,[e("div",H,[o(V,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:i(()=>[s(t).recentlySuccessful?(p(),_("div",I,[J,l(" Changes saved ")])):C("",!0)]),_:1})]),e("div",Q,[o(s(f),{href:r.route("email-sequence.index"),class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:i(()=>[l(" Cancel ")]),_:1},8,["href"]),o($,{disabled:s(t).processing,class:"inline-flex items-center px-4 py-2"},{default:i(()=>[l(" Save ")]),_:1},8,["disabled"])])])],32)])])]),_:1})],64))}};export{ae as default};
